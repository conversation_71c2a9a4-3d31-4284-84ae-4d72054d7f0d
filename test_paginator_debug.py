#!/usr/bin/env python3
"""
Тест для отладки проблемы с кнопками в пагинаторе
"""
import asyncio
from aiogram.types import InlineKeyboardButton, InlineKeyboardMarkup
from common.keyboards import get_main_menu_back_button

async def test_buttons():
    """Тестируем добавление кнопок"""
    
    # Создаем базовую клавиатуру (имитируем результат super().render_kb())
    base_kb = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="Урок 1", callback_data="lesson_1")],
        [InlineKeyboardButton(text="Урок 2", callback_data="lesson_2")],
        [InlineKeyboardButton(text="< 1 >", callback_data="page_nav")]
    ])
    
    print("Базовая клавиатура:")
    for i, row in enumerate(base_kb.inline_keyboard):
        print(f"  Ряд {i+1}: {[btn.text for btn in row]}")
    
    # Получаем кнопки главного меню
    main_menu_buttons = get_main_menu_back_button()
    print(f"\nКнопки главного меню: {main_menu_buttons}")
    print(f"Тип: {type(main_menu_buttons)}")
    
    # Добавляем кнопки
    base_kb.inline_keyboard.extend(main_menu_buttons)
    
    print("\nКлавиатура после добавления кнопок:")
    for i, row in enumerate(base_kb.inline_keyboard):
        print(f"  Ряд {i+1}: {[btn.text for btn in row]}")
    
    print(f"\nОбщее количество рядов: {len(base_kb.inline_keyboard)}")

if __name__ == "__main__":
    asyncio.run(test_buttons())
