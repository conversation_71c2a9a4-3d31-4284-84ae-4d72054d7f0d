"""
Обработчик callback'ов пагинации для добавления дополнительных кнопок
"""
from aiogram import Router
from aiogram.types import CallbackQuery
from aiogram.filters import Text
from aiogramx import Paginator
from common.keyboards import get_main_menu_back_button

router = Router()


@router.callback_query(Text(startswith="paginator:"))
async def handle_paginator_callback(callback: CallbackQuery):
    """
    Перехватываем callback'ы пагинации и добавляем дополнительные кнопки
    """
    # Позволяем aiogramx обработать callback
    await Paginator.handle_callback(callback)
    
    # Получаем обновленную клавиатуру
    if callback.message and callback.message.reply_markup:
        # Проверяем, что это пагинатор уроков (по наличию характерных кнопок)
        keyboard = callback.message.reply_markup
        
        # Ищем кнопки пагинации в последнем ряду
        has_pagination = False
        if keyboard.inline_keyboard:
            last_row = keyboard.inline_keyboard[-1]
            pagination_buttons = ["<<", "<", ">", ">>"]
            for button in last_row:
                if any(symbol in button.text for symbol in pagination_buttons):
                    has_pagination = True
                    break
        
        # Если это пагинатор уроков, добавляем наши кнопки
        if has_pagination:
            # Проверяем, что наши кнопки еще не добавлены
            has_our_buttons = False
            for row in keyboard.inline_keyboard:
                for button in row:
                    if "🏠 Главное меню" in button.text or "⬅️ Назад" in button.text:
                        has_our_buttons = True
                        break
                if has_our_buttons:
                    break
            
            # Добавляем кнопки только если их еще нет
            if not has_our_buttons:
                main_menu_buttons = get_main_menu_back_button()
                keyboard.inline_keyboard.extend(main_menu_buttons)
                
                # Обновляем сообщение с новой клавиатурой
                await callback.message.edit_reply_markup(reply_markup=keyboard)
