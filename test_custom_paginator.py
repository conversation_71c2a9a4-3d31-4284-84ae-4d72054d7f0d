#!/usr/bin/env python3
"""
Тест кастомного пагинатора с интеграцией навигации
"""
import asyncio
from aiogram.types import InlineKeyboardButton
from student.handlers.homework import CustomLessonsPaginator

async def test_custom_paginator():
    """Тестируем кастомный пагинатор с вашими кнопками навигации"""
    
    # Создаем тестовые данные
    test_lessons = [
        {"id": 1, "name": "Планиметрия"},
        {"id": 2, "name": "Стереометрия"},
        {"id": 3, "name": "Векторная алгебра"},
        {"id": 4, "name": "Линейная алгебра"},
        {"id": 5, "name": "Комбинаторика и вероятность"},
        {"id": 6, "name": "Урок 6"},
        {"id": 7, "name": "Урок 7"},
        {"id": 8, "name": "Урок 8"},
        {"id": 9, "name": "Урок 9"},
        {"id": 10, "name": "Урок 10"},
        {"id": 11, "name": "Урок 11"},
        {"id": 12, "name": "Урок 12"},
    ]
    
    async def get_lessons_buttons(cur_page: int, per_page: int):
        """Получает кнопки уроков для текущей страницы"""
        start_idx = (cur_page - 1) * per_page
        end_idx = start_idx + per_page
        page_lessons = test_lessons[start_idx:end_idx]
        
        buttons = []
        for lesson in page_lessons:
            buttons.append(InlineKeyboardButton(
                text=lesson["name"],
                callback_data=f"lesson_{lesson['id']}"
            ))
        
        return buttons
    
    async def get_lessons_count():
        """Получает общее количество уроков"""
        return len(test_lessons)
    
    async def on_lesson_select(callback, lesson_callback_data: str):
        """Обработчик выбора урока"""
        print(f"Выбран урок: {lesson_callback_data}")
    
    # Создаем кастомный пагинатор
    paginator = CustomLessonsPaginator(
        per_page=5,  # 5 уроков на страницу
        per_row=1,   # По одному уроку в ряду
        lazy_data=get_lessons_buttons,
        lazy_count=get_lessons_count,
        on_select=on_lesson_select,
        on_back=None,  # Без собственной кнопки "Назад"
        lang="ru"
    )
    
    # Тестируем рендеринг первой страницы
    keyboard = await paginator.render_kb(page=1)
    print("✅ Кастомный пагинатор создан успешно!")
    print(f"Количество рядов кнопок: {len(keyboard.inline_keyboard)}")
    
    # Выводим структуру клавиатуры
    for i, row in enumerate(keyboard.inline_keyboard):
        print(f"Ряд {i+1}: {[btn.text for btn in row]}")
    
    # Проверяем, что добавились ваши кнопки навигации
    last_two_rows = keyboard.inline_keyboard[-2:]
    navigation_texts = []
    for row in last_two_rows:
        navigation_texts.extend([btn.text for btn in row])
    
    print(f"\nКнопки навигации: {navigation_texts}")
    
    # Проверяем наличие ваших стандартных кнопок
    expected_buttons = ["⬅️ Назад", "🏠 Главное меню"]
    found_buttons = [btn for btn in expected_buttons if btn in navigation_texts]
    
    if len(found_buttons) == len(expected_buttons):
        print("✅ Все стандартные кнопки навигации найдены!")
    else:
        print(f"❌ Не найдены кнопки: {set(expected_buttons) - set(found_buttons)}")
    
    return paginator

if __name__ == "__main__":
    asyncio.run(test_custom_paginator())
